import React from 'react';
import { <PERSON>, Card<PERSON>ontent, Card<PERSON><PERSON>er, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { TrendingUp, TrendingDown, Target, Award, AlertTriangle, CheckCircle } from 'lucide-react';

interface KPISummaryProps {
  period: 'MTD' | 'QTD' | 'YTD';
  userType?: 'franchisor' | 'franchisee';
}

const KPISummary: React.FC<KPISummaryProps> = ({ period, userType = 'franchisor' }) => {
  const formatCurrency = (value: number) => {
    return new Intl.NumberFormat('en-PH', {
      style: 'currency',
      currency: 'PHP',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0
    }).format(value);
  };

  const insights = {
    franchisor: {
      MTD: {
        totalSales: 835000,
        growth: 12.5,
        target: 800000,
        franchisees: 52,
        topPerformer: 'Siomai Shop - BGC',
        insights: [
          { type: 'success', message: 'Sales exceeded target by 4.4%' },
          { type: 'info', message: '3 new franchisees added this month' },
          { type: 'warning', message: '2 franchisees below 80% target' }
        ]
      },
      QTD: {
        totalSales: 2820000,
        growth: 18.2,
        target: 2550000,
        franchisees: 52,
        topPerformer: 'Coffee Shop - Ortigas',
        insights: [
          { type: 'success', message: 'Quarter target exceeded by 10.6%' },
          { type: 'success', message: 'Best quarter performance in 2024' },
          { type: 'info', message: 'Coffee Shop brand leading growth' }
        ]
      },
      YTD: {
        totalSales: 12280000,
        growth: 15.8,
        target: 11550000,
        franchisees: 65,
        topPerformer: 'Siomai Shop Network',
        insights: [
          { type: 'success', message: 'Annual target achieved 2 months early' },
          { type: 'success', message: '13 new franchisees added this year' },
          { type: 'info', message: 'Expansion rate: 25% year-over-year' }
        ]
      }
    },
    franchisee: {
      MTD: {
        totalSales: 84700,
        growth: 15.2,
        target: 80000,
        orders: 643,
        topProduct: 'Siomai Regular',
        insights: [
          { type: 'success', message: 'Monthly target exceeded by 5.9%' },
          { type: 'info', message: 'Average order value: ₱132' },
          { type: 'warning', message: 'Inventory running low on sauce packets' }
        ]
      },
      QTD: {
        totalSales: 282600,
        growth: 18.8,
        target: 255000,
        orders: 2130,
        topProduct: 'Siomai Regular',
        insights: [
          { type: 'success', message: 'Best quarter performance this year' },
          { type: 'success', message: 'Customer retention rate: 85%' },
          { type: 'info', message: 'Peak hours: 11AM-2PM, 6PM-8PM' }
        ]
      },
      YTD: {
        totalSales: 1230000,
        growth: 16.4,
        target: 1155000,
        orders: 9309,
        topProduct: 'Siomai Regular',
        insights: [
          { type: 'success', message: 'Annual target achieved early' },
          { type: 'success', message: 'Top 10% performer in network' },
          { type: 'info', message: 'Eligible for performance bonus' }
        ]
      }
    }
  };

  const currentData = insights[userType][period];
  const targetAchievement = (currentData.totalSales / currentData.target) * 100;

  const getInsightIcon = (type: string) => {
    switch (type) {
      case 'success':
        return <CheckCircle className="w-4 h-4 text-green-600" />;
      case 'warning':
        return <AlertTriangle className="w-4 h-4 text-yellow-600" />;
      case 'info':
      default:
        return <Target className="w-4 h-4 text-blue-600" />;
    }
  };

  const getInsightColor = (type: string) => {
    switch (type) {
      case 'success':
        return 'bg-green-50 border-green-200 text-green-800';
      case 'warning':
        return 'bg-yellow-50 border-yellow-200 text-yellow-800';
      case 'info':
      default:
        return 'bg-blue-50 border-blue-200 text-blue-800';
    }
  };

  return (
    <div className="grid lg:grid-cols-3 gap-6">
      {/* Performance Summary */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Award className="w-5 h-5 text-yellow-600" />
            <span>Performance Summary</span>
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="text-center">
            <div className="text-3xl font-bold text-gray-900">
              {formatCurrency(currentData.totalSales)}
            </div>
            <p className="text-sm text-gray-600">Total Sales ({period})</p>
          </div>
          
          <div className="flex items-center justify-center space-x-2">
            {currentData.growth > 0 ? (
              <TrendingUp className="w-4 h-4 text-green-600" />
            ) : (
              <TrendingDown className="w-4 h-4 text-red-600" />
            )}
            <span className={`font-medium ${currentData.growth > 0 ? 'text-green-600' : 'text-red-600'}`}>
              {currentData.growth > 0 ? '+' : ''}{currentData.growth}%
            </span>
            <span className="text-gray-600">vs last period</span>
          </div>

          <div className="space-y-2">
            <div className="flex justify-between text-sm">
              <span>Target Achievement</span>
              <span className="font-medium">{targetAchievement.toFixed(1)}%</span>
            </div>
            <div className="w-full bg-gray-200 rounded-full h-2">
              <div 
                className={`h-2 rounded-full ${targetAchievement >= 100 ? 'bg-green-500' : targetAchievement >= 80 ? 'bg-yellow-500' : 'bg-red-500'}`}
                style={{ width: `${Math.min(targetAchievement, 100)}%` }}
              ></div>
            </div>
          </div>

          <Badge className={targetAchievement >= 100 ? 'bg-green-100 text-green-800' : targetAchievement >= 80 ? 'bg-yellow-100 text-yellow-800' : 'bg-red-100 text-red-800'}>
            {targetAchievement >= 100 ? 'Exceeding Target' : targetAchievement >= 80 ? 'On Track' : 'Below Target'}
          </Badge>
        </CardContent>
      </Card>

      {/* Key Metrics */}
      <Card>
        <CardHeader>
          <CardTitle>Key Metrics</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-2 gap-4">
            <div className="text-center p-3 bg-gray-50 rounded-lg">
              <div className="text-lg font-bold text-gray-900">
                {formatCurrency(currentData.target)}
              </div>
              <p className="text-xs text-gray-600">Target</p>
            </div>
            
            {userType === 'franchisor' ? (
              <div className="text-center p-3 bg-gray-50 rounded-lg">
                <div className="text-lg font-bold text-gray-900">
                  {currentData.franchisees}
                </div>
                <p className="text-xs text-gray-600">Active Franchisees</p>
              </div>
            ) : (
              <div className="text-center p-3 bg-gray-50 rounded-lg">
                <div className="text-lg font-bold text-gray-900">
                  {currentData.orders?.toLocaleString()}
                </div>
                <p className="text-xs text-gray-600">Total Orders</p>
              </div>
            )}
          </div>

          <div className="p-3 bg-blue-50 rounded-lg">
            <p className="text-sm font-medium text-blue-900">
              {userType === 'franchisor' ? 'Top Performer' : 'Best Product'}
            </p>
            <p className="text-xs text-blue-700">
              {userType === 'franchisor' ? currentData.topPerformer : currentData.topProduct}
            </p>
          </div>
        </CardContent>
      </Card>

      {/* Insights & Alerts */}
      <Card>
        <CardHeader>
          <CardTitle>Insights & Alerts</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-3">
            {currentData.insights.map((insight, index) => (
              <div 
                key={index}
                className={`p-3 rounded-lg border ${getInsightColor(insight.type)}`}
              >
                <div className="flex items-start space-x-2">
                  {getInsightIcon(insight.type)}
                  <p className="text-sm font-medium">{insight.message}</p>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default KPISummary;

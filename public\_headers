# Security headers for production deployment
/*
  # Content Security Policy
  Content-Security-Policy: default-src 'self'; script-src 'self' 'unsafe-inline' 'unsafe-eval' https://cdn.gpteng.co https://www.googletagmanager.com https://www.google-analytics.com; style-src 'self' 'unsafe-inline' https://fonts.googleapis.com; font-src 'self' https://fonts.gstatic.com; img-src 'self' data: https: blob:; connect-src 'self' https://api.franchisehub.ph https://www.google-analytics.com; frame-src 'self' https://www.google.com; object-src 'none'; base-uri 'self'; form-action 'self';
  
  # Prevent clickjacking
  X-Frame-Options: DENY
  
  # Prevent MIME type sniffing
  X-Content-Type-Options: nosniff
  
  # Enable XSS protection
  X-XSS-Protection: 1; mode=block
  
  # Referrer policy
  Referrer-Policy: strict-origin-when-cross-origin
  
  # Permissions policy
  Permissions-Policy: camera=(), microphone=(), geolocation=(), payment=(), usb=()
  
  # Strict Transport Security (HTTPS only)
  Strict-Transport-Security: max-age=31536000; includeSubDomains; preload
  
  # Cache control for static assets
  Cache-Control: public, max-age=31536000, immutable

# Specific headers for HTML files
/*.html
  Cache-Control: public, max-age=0, must-revalidate

# Specific headers for API routes (if any)
/api/*
  Cache-Control: no-cache, no-store, must-revalidate
  Pragma: no-cache
  Expires: 0

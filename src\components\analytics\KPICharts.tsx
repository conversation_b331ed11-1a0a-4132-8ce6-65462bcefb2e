import React, { useState } from 'react';
import { <PERSON>, <PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON>er, Card<PERSON><PERSON><PERSON> } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
// Chart imports removed - charts not displaying properly
import { TrendingUp, TrendingDown, Calendar, DollarSign, Users, Package, Target, Award } from 'lucide-react';
import KPISummary from './KPISummary';

// Enhanced mock data with realistic business patterns
const generateSalesData = (period: 'MTD' | 'QTD' | 'YTD') => {
  const baseData = {
    MTD: [
      { name: 'Week 1', sales: 2850000, target: 2500000, franchisees: 78, growth: 18.5 },
      { name: 'Week 2', sales: 3420000, target: 2500000, franchisees: 79, growth: 22.8 },
      { name: 'Week 3', sales: 3180000, target: 2500000, franchisees: 81, growth: 19.2 },
      { name: 'Week 4', sales: 4250000, target: 2500000, franchisees: 83, growth: 28.4 }
    ],
    QTD: [
      { name: 'January', sales: 12850000, target: 11500000, franchisees: 78, growth: 15.2 },
      { name: 'February', sales: 14920000, target: 12000000, franchisees: 81, growth: 18.7 },
      { name: 'March', sales: 16750000, target: 13500000, franchisees: 83, growth: 22.4 }
    ],
    YTD: [
      { name: 'Q1 2024', sales: 44520000, target: 37000000, franchisees: 83, growth: 20.3 },
      { name: 'Q2 2024', sales: 52150000, target: 42000000, franchisees: 89, growth: 24.2 },
      { name: 'Q3 2024', sales: 58420000, target: 48000000, franchisees: 94, growth: 21.8 },
      { name: 'Q4 2024', sales: 48900000, target: 52000000, franchisees: 97, growth: 16.5 }
    ]
  };
  return baseData[period];
};

const brandPerformanceData = [
  { name: 'Siomai King', value: 38, sales: 77520000, color: '#ef4444', growth: 24.5, locations: 32, avgRevenue: 2422500, topProduct: 'Siomai King Special' },
  { name: 'Coffee Masters', value: 29, sales: 59157000, color: '#f59e0b', growth: 31.2, locations: 25, avgRevenue: 2366280, topProduct: 'Premium Iced Coffee' },
  { name: 'Burger Express', value: 21, sales: 42838000, color: '#10b981', growth: 18.7, locations: 19, avgRevenue: 2254631, topProduct: 'Signature Burger' },
  { name: 'Fresh Juice Bar', value: 12, sales: 24479000, color: '#8b5cf6', growth: 22.1, locations: 15, avgRevenue: 1631933, topProduct: 'Fresh Lemonade' }
];

// Network-wide product performance
const networkProductData = [
  { name: 'Siomai King Special', sales: 28650000, orders: 125600, brands: 32, avgPrice: 228, category: 'Signature' },
  { name: 'Premium Iced Coffee', sales: 22340000, orders: 142800, brands: 25, avgPrice: 156, category: 'Beverages' },
  { name: 'Signature Burger', sales: 18920000, orders: 78400, brands: 19, avgPrice: 241, category: 'Meals' },
  { name: 'Spicy Siomai Deluxe', sales: 16780000, orders: 72300, brands: 32, avgPrice: 232, category: 'Signature' },
  { name: 'Premium Rice Bowls', sales: 14560000, orders: 58900, brands: 19, avgPrice: 247, category: 'Meals' },
  { name: 'Fresh Lemonade', sales: 12890000, orders: 84600, brands: 15, avgPrice: 152, category: 'Beverages' },
  { name: 'Chicken Teriyaki', sales: 11230000, orders: 47800, brands: 19, avgPrice: 235, category: 'Meals' },
  { name: 'Iced Tea Premium', sales: 9870000, orders: 65800, brands: 25, avgPrice: 150, category: 'Beverages' }
];

// Regional performance data
const regionalData = [
  { region: 'Metro Manila', sales: 89650000, franchisees: 42, growth: 28.4, topBrand: 'Siomai King' },
  { region: 'Cebu', sales: 45230000, franchisees: 18, growth: 31.2, topBrand: 'Coffee Masters' },
  { region: 'Davao', sales: 32180000, franchisees: 15, growth: 24.7, topBrand: 'Burger Express' },
  { region: 'Iloilo', sales: 21890000, franchisees: 12, growth: 26.1, topBrand: 'Fresh Juice Bar' },
  { region: 'Baguio', sales: 15040000, franchisees: 10, growth: 22.3, topBrand: 'Coffee Masters' }
];

const kpiData = {
  MTD: {
    totalSales: 13700000,
    growth: 22.1,
    target: 10000000,
    franchisees: 83,
    avgPerFranchisee: 165060,
    newFranchisees: 5,
    topPerformer: 'Siomai King - BGC'
  },
  QTD: {
    totalSales: 44520000,
    growth: 18.9,
    target: 37000000,
    franchisees: 83,
    avgPerFranchisee: 536385,
    newFranchisees: 8,
    topPerformer: 'Coffee Masters - Ortigas'
  },
  YTD: {
    totalSales: 203990000,
    growth: 20.7,
    target: 179000000,
    franchisees: 97,
    avgPerFranchisee: 2103000,
    newFranchisees: 22,
    topPerformer: 'Siomai King Network'
  }
};

interface KPIChartsProps {
  userType?: 'franchisor' | 'franchisee';
}

const KPICharts: React.FC<KPIChartsProps> = ({ userType = 'franchisor' }) => {
  const [selectedPeriod, setSelectedPeriod] = useState<'MTD' | 'QTD' | 'YTD'>('MTD');

  const currentData = generateSalesData(selectedPeriod);
  const currentKPI = kpiData[selectedPeriod];
  const targetAchievement = (currentKPI.totalSales / currentKPI.target) * 100;

  // Ensure brand performance data is always available
  const brandDataToUse = brandPerformanceData || [];
  const networkProductDataToUse = networkProductData || [];
  const regionalDataToUse = regionalData || [];

  const formatCurrency = (value: number) => {
    return new Intl.NumberFormat('en-PH', {
      style: 'currency',
      currency: 'PHP',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0
    }).format(value);
  };

  const chartConfig = {
    sales: {
      label: "Sales",
      color: "#2563eb",
    },
    target: {
      label: "Target",
      color: "#dc2626",
    },
    franchisees: {
      label: "Active Franchisees",
      color: "#16a34a",
    }
  };

  return (
    <div className="space-y-6">
      {/* Period Selection */}
      <div className="flex items-center justify-between">
        <h2 className="text-2xl font-bold text-gray-900">Analytics Dashboard</h2>
        <Tabs value={selectedPeriod} onValueChange={(value) => setSelectedPeriod(value as 'MTD' | 'QTD' | 'YTD')}>
          <TabsList className="grid w-full grid-cols-3">
            <TabsTrigger value="MTD">Month to Date</TabsTrigger>
            <TabsTrigger value="QTD">Quarter to Date</TabsTrigger>
            <TabsTrigger value="YTD">Year to Date</TabsTrigger>
          </TabsList>
        </Tabs>
      </div>

      {/* KPI Summary Cards */}
      <div className="grid md:grid-cols-4 gap-6">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Sales ({selectedPeriod})</CardTitle>
            <DollarSign className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{formatCurrency(currentKPI.totalSales)}</div>
            <div className="flex items-center space-x-2 text-xs">
              {currentKPI.growth > 0 ? (
                <TrendingUp className="h-3 w-3 text-green-600" />
              ) : (
                <TrendingDown className="h-3 w-3 text-red-600" />
              )}
              <span className={currentKPI.growth > 0 ? 'text-green-600' : 'text-red-600'}>
                {currentKPI.growth > 0 ? '+' : ''}{currentKPI.growth}% vs last period
              </span>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Target Achievement</CardTitle>
            <Target className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{targetAchievement.toFixed(1)}%</div>
            <div className="text-xs text-muted-foreground">
              Target: {formatCurrency(currentKPI.target)}
            </div>
            <div className="w-full bg-gray-200 rounded-full h-2 mt-2">
              <div
                className={`h-2 rounded-full ${targetAchievement >= 100 ? 'bg-green-500' : targetAchievement >= 80 ? 'bg-yellow-500' : 'bg-red-500'}`}
                style={{ width: `${Math.min(targetAchievement, 100)}%` }}
              ></div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Active Franchisees</CardTitle>
            <Users className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{currentKPI.franchisees}</div>
            <p className="text-xs text-muted-foreground">
              Avg: {formatCurrency(currentKPI.avgPerFranchisee)} per franchisee
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Performance Status</CardTitle>
            <Package className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <Badge className={targetAchievement >= 100 ? 'bg-green-100 text-green-800' : targetAchievement >= 80 ? 'bg-yellow-100 text-yellow-800' : 'bg-red-100 text-red-800'}>
              {targetAchievement >= 100 ? 'Exceeding' : targetAchievement >= 80 ? 'On Track' : 'Below Target'}
            </Badge>
            <p className="text-xs text-muted-foreground mt-2">
              {targetAchievement >= 100 ? '🎉 Great performance!' : targetAchievement >= 80 ? '📈 Good progress' : '⚠️ Needs attention'}
            </p>
          </CardContent>
        </Card>
      </div>



      {/* Key Business Insights - Streamlined */}
      <div className="grid lg:grid-cols-3 gap-8 mt-8">
        {/* Regional Performance */}
        <Card className="border-0 shadow-xl bg-gradient-to-br from-purple-50 to-pink-50">
          <CardHeader className="pb-4">
            <CardTitle className="text-xl font-bold text-gray-800 flex items-center">
              <Target className="w-6 h-6 mr-3 text-purple-600" />
              Regional Leaders
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {regionalDataToUse.slice(0, 3).map((region, index) => (
                <div key={index} className="flex items-center justify-between p-4 bg-white rounded-xl shadow-sm">
                  <div>
                    <p className="font-bold text-gray-800">{region.region}</p>
                    <p className="text-sm text-gray-600">{region.franchisees} locations</p>
                  </div>
                  <div className="text-right">
                    <p className="font-bold text-lg">{formatCurrency(region.sales)}</p>
                    <p className="text-sm text-green-600">+{region.growth}%</p>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>

        {/* Star Product */}
        <Card className="border-0 shadow-xl bg-gradient-to-br from-yellow-50 to-orange-50">
          <CardHeader className="pb-4">
            <CardTitle className="text-xl font-bold text-gray-800 flex items-center">
              <Award className="w-6 h-6 mr-3 text-yellow-600" />
              Network Champion
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-center space-y-4">
              <div className="p-6 bg-white rounded-xl shadow-sm">
                <div className="text-2xl font-bold text-gray-800">Siomai King Special</div>
                <div className="text-3xl font-bold text-yellow-600 mt-2">{formatCurrency(28650000)}</div>
                <p className="text-gray-600 mt-1">Network Revenue (YTD)</p>
                <div className="flex justify-center space-x-4 mt-4">
                  <Badge className="bg-green-100 text-green-800 px-3 py-1">125.6K Orders</Badge>
                  <Badge className="bg-blue-100 text-blue-800 px-3 py-1">32 Locations</Badge>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Growth Metrics */}
        <Card className="border-0 shadow-xl bg-gradient-to-br from-emerald-50 to-teal-50">
          <CardHeader className="pb-4">
            <CardTitle className="text-xl font-bold text-gray-800 flex items-center">
              <TrendingUp className="w-6 h-6 mr-3 text-emerald-600" />
              Growth Highlights
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="p-4 bg-white rounded-xl shadow-sm">
                <div className="flex items-center justify-between">
                  <span className="text-gray-600">Network Growth</span>
                  <span className="text-2xl font-bold text-emerald-600">+26.8%</span>
                </div>
              </div>
              <div className="p-4 bg-white rounded-xl shadow-sm">
                <div className="flex items-center justify-between">
                  <span className="text-gray-600">New Franchisees</span>
                  <span className="text-2xl font-bold text-blue-600">+18</span>
                </div>
              </div>
              <div className="p-4 bg-white rounded-xl shadow-sm">
                <div className="flex items-center justify-between">
                  <span className="text-gray-600">Product Portfolio</span>
                  <span className="text-2xl font-bold text-purple-600">156</span>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* KPI Summary */}
      <div className="mt-8">
        <KPISummary period={selectedPeriod} userType={userType} />
      </div>
    </div>
  );
};

export default KPICharts;

import React, { useState } from 'react';
import { <PERSON>, Card<PERSON>ontent, Card<PERSON><PERSON><PERSON>, Card<PERSON>itle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { ChartContainer, ChartTooltip, ChartTooltipContent } from '@/components/ui/chart';
import {
  LineChart,
  Line,
  AreaChart,
  Area,
  BarChart,
  Bar,
  PieChart,
  Pie,
  Cell,
  XAxis,
  YAxis,
  CartesianGrid,
  ResponsiveContainer,
  Legend
} from 'recharts';
import { TrendingUp, TrendingDown, Calendar, DollarSign, Users, Package, Target } from 'lucide-react';
import KPISummary from './KPISummary';

// Enhanced mock data with realistic business patterns
const generateSalesData = (period: 'MTD' | 'QTD' | 'YTD') => {
  const baseData = {
    MTD: [
      { name: 'Week 1', sales: 2850000, target: 2500000, franchisees: 78, growth: 18.5 },
      { name: 'Week 2', sales: 3420000, target: 2500000, franchisees: 79, growth: 22.8 },
      { name: 'Week 3', sales: 3180000, target: 2500000, franchisees: 81, growth: 19.2 },
      { name: 'Week 4', sales: 4250000, target: 2500000, franchisees: 83, growth: 28.4 }
    ],
    QTD: [
      { name: 'January', sales: 12850000, target: 11500000, franchisees: 78, growth: 15.2 },
      { name: 'February', sales: 14920000, target: 12000000, franchisees: 81, growth: 18.7 },
      { name: 'March', sales: 16750000, target: 13500000, franchisees: 83, growth: 22.4 }
    ],
    YTD: [
      { name: 'Q1 2024', sales: 44520000, target: 37000000, franchisees: 83, growth: 20.3 },
      { name: 'Q2 2024', sales: 52150000, target: 42000000, franchisees: 89, growth: 24.2 },
      { name: 'Q3 2024', sales: 58420000, target: 48000000, franchisees: 94, growth: 21.8 },
      { name: 'Q4 2024', sales: 48900000, target: 52000000, franchisees: 97, growth: 16.5 }
    ]
  };
  return baseData[period];
};

const brandPerformanceData = [
  { name: 'Siomai King', value: 38, sales: 77520000, color: '#ef4444', growth: 24.5, locations: 32, avgRevenue: 2422500, topProduct: 'Siomai King Special' },
  { name: 'Coffee Masters', value: 29, sales: 59157000, color: '#f59e0b', growth: 31.2, locations: 25, avgRevenue: 2366280, topProduct: 'Premium Iced Coffee' },
  { name: 'Burger Express', value: 21, sales: 42838000, color: '#10b981', growth: 18.7, locations: 19, avgRevenue: 2254631, topProduct: 'Signature Burger' },
  { name: 'Fresh Juice Bar', value: 12, sales: 24479000, color: '#8b5cf6', growth: 22.1, locations: 15, avgRevenue: 1631933, topProduct: 'Fresh Lemonade' }
];

// Network-wide product performance
const networkProductData = [
  { name: 'Siomai King Special', sales: 28650000, orders: 125600, brands: 32, avgPrice: 228, category: 'Signature' },
  { name: 'Premium Iced Coffee', sales: 22340000, orders: 142800, brands: 25, avgPrice: 156, category: 'Beverages' },
  { name: 'Signature Burger', sales: 18920000, orders: 78400, brands: 19, avgPrice: 241, category: 'Meals' },
  { name: 'Spicy Siomai Deluxe', sales: 16780000, orders: 72300, brands: 32, avgPrice: 232, category: 'Signature' },
  { name: 'Premium Rice Bowls', sales: 14560000, orders: 58900, brands: 19, avgPrice: 247, category: 'Meals' },
  { name: 'Fresh Lemonade', sales: 12890000, orders: 84600, brands: 15, avgPrice: 152, category: 'Beverages' },
  { name: 'Chicken Teriyaki', sales: 11230000, orders: 47800, brands: 19, avgPrice: 235, category: 'Meals' },
  { name: 'Iced Tea Premium', sales: 9870000, orders: 65800, brands: 25, avgPrice: 150, category: 'Beverages' }
];

// Regional performance data
const regionalData = [
  { region: 'Metro Manila', sales: 89650000, franchisees: 42, growth: 28.4, topBrand: 'Siomai King' },
  { region: 'Cebu', sales: 45230000, franchisees: 18, growth: 31.2, topBrand: 'Coffee Masters' },
  { region: 'Davao', sales: 32180000, franchisees: 15, growth: 24.7, topBrand: 'Burger Express' },
  { region: 'Iloilo', sales: 21890000, franchisees: 12, growth: 26.1, topBrand: 'Fresh Juice Bar' },
  { region: 'Baguio', sales: 15040000, franchisees: 10, growth: 22.3, topBrand: 'Coffee Masters' }
];

const kpiData = {
  MTD: {
    totalSales: 13700000,
    growth: 22.1,
    target: 10000000,
    franchisees: 83,
    avgPerFranchisee: 165060,
    newFranchisees: 5,
    topPerformer: 'Siomai King - BGC'
  },
  QTD: {
    totalSales: 44520000,
    growth: 18.9,
    target: 37000000,
    franchisees: 83,
    avgPerFranchisee: 536385,
    newFranchisees: 8,
    topPerformer: 'Coffee Masters - Ortigas'
  },
  YTD: {
    totalSales: 203990000,
    growth: 20.7,
    target: 179000000,
    franchisees: 97,
    avgPerFranchisee: 2103000,
    newFranchisees: 22,
    topPerformer: 'Siomai King Network'
  }
};

interface KPIChartsProps {
  userType?: 'franchisor' | 'franchisee';
}

const KPICharts: React.FC<KPIChartsProps> = ({ userType = 'franchisor' }) => {
  const [selectedPeriod, setSelectedPeriod] = useState<'MTD' | 'QTD' | 'YTD'>('MTD');

  const currentData = generateSalesData(selectedPeriod);
  const currentKPI = kpiData[selectedPeriod];
  const targetAchievement = (currentKPI.totalSales / currentKPI.target) * 100;

  // Ensure brand performance data is always available
  const brandDataToUse = brandPerformanceData || [];
  const networkProductDataToUse = networkProductData || [];
  const regionalDataToUse = regionalData || [];

  const formatCurrency = (value: number) => {
    return new Intl.NumberFormat('en-PH', {
      style: 'currency',
      currency: 'PHP',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0
    }).format(value);
  };

  const chartConfig = {
    sales: {
      label: "Sales",
      color: "#2563eb",
    },
    target: {
      label: "Target",
      color: "#dc2626",
    },
    franchisees: {
      label: "Active Franchisees",
      color: "#16a34a",
    }
  };

  return (
    <div className="space-y-6">
      {/* Period Selection */}
      <div className="flex items-center justify-between">
        <h2 className="text-2xl font-bold text-gray-900">Analytics Dashboard</h2>
        <Tabs value={selectedPeriod} onValueChange={(value) => setSelectedPeriod(value as 'MTD' | 'QTD' | 'YTD')}>
          <TabsList className="grid w-full grid-cols-3">
            <TabsTrigger value="MTD">Month to Date</TabsTrigger>
            <TabsTrigger value="QTD">Quarter to Date</TabsTrigger>
            <TabsTrigger value="YTD">Year to Date</TabsTrigger>
          </TabsList>
        </Tabs>
      </div>

      {/* KPI Summary Cards */}
      <div className="grid md:grid-cols-4 gap-6">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Sales ({selectedPeriod})</CardTitle>
            <DollarSign className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{formatCurrency(currentKPI.totalSales)}</div>
            <div className="flex items-center space-x-2 text-xs">
              {currentKPI.growth > 0 ? (
                <TrendingUp className="h-3 w-3 text-green-600" />
              ) : (
                <TrendingDown className="h-3 w-3 text-red-600" />
              )}
              <span className={currentKPI.growth > 0 ? 'text-green-600' : 'text-red-600'}>
                {currentKPI.growth > 0 ? '+' : ''}{currentKPI.growth}% vs last period
              </span>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Target Achievement</CardTitle>
            <Target className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{targetAchievement.toFixed(1)}%</div>
            <div className="text-xs text-muted-foreground">
              Target: {formatCurrency(currentKPI.target)}
            </div>
            <div className="w-full bg-gray-200 rounded-full h-2 mt-2">
              <div
                className={`h-2 rounded-full ${targetAchievement >= 100 ? 'bg-green-500' : targetAchievement >= 80 ? 'bg-yellow-500' : 'bg-red-500'}`}
                style={{ width: `${Math.min(targetAchievement, 100)}%` }}
              ></div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Active Franchisees</CardTitle>
            <Users className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{currentKPI.franchisees}</div>
            <p className="text-xs text-muted-foreground">
              Avg: {formatCurrency(currentKPI.avgPerFranchisee)} per franchisee
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Performance Status</CardTitle>
            <Package className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <Badge className={targetAchievement >= 100 ? 'bg-green-100 text-green-800' : targetAchievement >= 80 ? 'bg-yellow-100 text-yellow-800' : 'bg-red-100 text-red-800'}>
              {targetAchievement >= 100 ? 'Exceeding' : targetAchievement >= 80 ? 'On Track' : 'Below Target'}
            </Badge>
            <p className="text-xs text-muted-foreground mt-2">
              {targetAchievement >= 100 ? '🎉 Great performance!' : targetAchievement >= 80 ? '📈 Good progress' : '⚠️ Needs attention'}
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Charts Grid */}
      <div className="grid lg:grid-cols-2 gap-6">
        {/* Sales Trend Chart */}
        <Card>
          <CardHeader>
            <CardTitle>Sales Trend vs Target ({selectedPeriod})</CardTitle>
          </CardHeader>
          <CardContent>
            <ChartContainer config={chartConfig} className="h-[300px]">
              <LineChart data={currentData}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="name" />
                <YAxis tickFormatter={(value) => `₱${(value / 1000)}K`} />
                <ChartTooltip
                  content={<ChartTooltipContent />}
                  formatter={(value: number, name: string) => [
                    formatCurrency(value),
                    name === 'sales' ? 'Sales' : 'Target'
                  ]}
                />
                <Line
                  type="monotone"
                  dataKey="sales"
                  stroke="var(--color-sales)"
                  strokeWidth={3}
                  dot={{ fill: "var(--color-sales)", strokeWidth: 2, r: 4 }}
                />
                <Line
                  type="monotone"
                  dataKey="target"
                  stroke="var(--color-target)"
                  strokeWidth={2}
                  strokeDasharray="5 5"
                  dot={{ fill: "var(--color-target)", strokeWidth: 2, r: 3 }}
                />
              </LineChart>
            </ChartContainer>
          </CardContent>
        </Card>

        {/* Brand Performance Pie Chart */}
        <Card>
          <CardHeader>
            <CardTitle>Revenue by Brand</CardTitle>
          </CardHeader>
          <CardContent>
            <ChartContainer config={chartConfig} className="h-[300px]">
              <PieChart>
                <Pie
                  data={brandDataToUse}
                  cx="50%"
                  cy="50%"
                  outerRadius={80}
                  dataKey="value"
                  label={({ name, value }) => `${name}: ${value}%`}
                >
                  {brandDataToUse.map((entry, index) => (
                    <Cell key={`cell-${index}`} fill={entry.color} />
                  ))}
                </Pie>
                <ChartTooltip
                  content={<ChartTooltipContent />}
                  formatter={(value: number, name: string, props: any) => [
                    `${value}% (${formatCurrency(props.payload.sales)})`,
                    props.payload.name
                  ]}
                />
              </PieChart>
            </ChartContainer>
          </CardContent>
        </Card>

        {/* Franchisee Growth Chart */}
        <Card>
          <CardHeader>
            <CardTitle>Franchisee Growth ({selectedPeriod})</CardTitle>
          </CardHeader>
          <CardContent>
            <ChartContainer config={chartConfig} className="h-[300px]">
              <AreaChart data={currentData}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="name" />
                <YAxis />
                <ChartTooltip
                  content={<ChartTooltipContent />}
                  formatter={(value: number) => [value, 'Active Franchisees']}
                />
                <Area
                  type="monotone"
                  dataKey="franchisees"
                  stroke="var(--color-franchisees)"
                  fill="var(--color-franchisees)"
                  fillOpacity={0.3}
                />
              </AreaChart>
            </ChartContainer>
          </CardContent>
        </Card>

        {/* Performance Comparison Bar Chart */}
        <Card>
          <CardHeader>
            <CardTitle>Sales vs Target Comparison</CardTitle>
          </CardHeader>
          <CardContent>
            <ChartContainer config={chartConfig} className="h-[300px]">
              <BarChart data={currentData}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="name" />
                <YAxis tickFormatter={(value) => `₱${(value / 1000)}K`} />
                <ChartTooltip
                  content={<ChartTooltipContent />}
                  formatter={(value: number, name: string) => [
                    formatCurrency(value),
                    name === 'sales' ? 'Actual Sales' : 'Target'
                  ]}
                />
                <Bar dataKey="target" fill="var(--color-target)" opacity={0.6} />
                <Bar dataKey="sales" fill="var(--color-sales)" />
              </BarChart>
            </ChartContainer>
          </CardContent>
        </Card>
      </div>

      {/* Network Product Performance */}
      <div className="grid lg:grid-cols-2 gap-6 mt-6">
        {/* Top Network Products */}
        <Card>
          <CardHeader>
            <CardTitle>Top Network Products (YTD)</CardTitle>
          </CardHeader>
          <CardContent>
            <ChartContainer config={chartConfig} className="h-[350px]">
              <BarChart data={networkProductDataToUse.slice(0, 6)} layout="horizontal">
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis type="number" tickFormatter={(value) => `₱${(value / 1000000)}M`} />
                <YAxis dataKey="name" type="category" width={120} />
                <ChartTooltip
                  content={<ChartTooltipContent />}
                  formatter={(value: number, name: string, props: any) => {
                    if (name === 'sales') return [formatCurrency(value), 'Network Sales'];
                    return [value, name];
                  }}
                />
                <Bar dataKey="sales" fill="var(--color-sales)" />
              </BarChart>
            </ChartContainer>
          </CardContent>
        </Card>

        {/* Regional Performance */}
        <Card>
          <CardHeader>
            <CardTitle>Regional Performance (YTD)</CardTitle>
          </CardHeader>
          <CardContent>
            <ChartContainer config={chartConfig} className="h-[350px]">
              <BarChart data={regionalDataToUse}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="region" />
                <YAxis tickFormatter={(value) => `₱${(value / 1000000)}M`} />
                <ChartTooltip
                  content={<ChartTooltipContent />}
                  formatter={(value: number, name: string, props: any) => {
                    if (name === 'sales') return [formatCurrency(value), 'Regional Sales'];
                    if (name === 'franchisees') return [value, 'Franchisees'];
                    if (name === 'growth') return [`${value}%`, 'Growth Rate'];
                    return [value, name];
                  }}
                />
                <Bar dataKey="sales" fill="var(--color-sales)" />
              </BarChart>
            </ChartContainer>
          </CardContent>
        </Card>
      </div>

      {/* Product Insights Grid */}
      <div className="grid lg:grid-cols-4 gap-6 mt-6">
        {/* Network Product Stats */}
        <Card>
          <CardHeader>
            <CardTitle>Product Portfolio</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="text-center">
                <div className="text-2xl font-bold text-gray-900">156</div>
                <p className="text-sm text-gray-600">Total Products</p>
              </div>
              <div className="space-y-2">
                <div className="flex justify-between text-sm">
                  <span>Signature Items</span>
                  <span className="font-medium">42</span>
                </div>
                <div className="flex justify-between text-sm">
                  <span>Meals</span>
                  <span className="font-medium">68</span>
                </div>
                <div className="flex justify-between text-sm">
                  <span>Beverages</span>
                  <span className="font-medium">46</span>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Top Performing Product */}
        <Card>
          <CardHeader>
            <CardTitle>Star Product</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-center space-y-2">
              <div className="text-lg font-bold text-blue-600">Siomai King Special</div>
              <div className="text-2xl font-bold">{formatCurrency(28650000)}</div>
              <p className="text-sm text-gray-600">Network Revenue</p>
              <Badge className="bg-green-100 text-green-800">125.6K Orders</Badge>
            </div>
          </CardContent>
        </Card>

        {/* Fastest Growing Product */}
        <Card>
          <CardHeader>
            <CardTitle>Rising Star</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-center space-y-2">
              <div className="text-lg font-bold text-orange-600">Premium Iced Coffee</div>
              <div className="text-2xl font-bold">+31.2%</div>
              <p className="text-sm text-gray-600">Growth Rate</p>
              <Badge className="bg-orange-100 text-orange-800">142.8K Orders</Badge>
            </div>
          </CardContent>
        </Card>

        {/* Product Innovation */}
        <Card>
          <CardHeader>
            <CardTitle>Innovation Pipeline</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              <div className="p-2 bg-blue-50 rounded-lg">
                <p className="text-sm font-medium text-blue-800">Q1 2025</p>
                <p className="text-xs text-blue-600">Premium Breakfast Line</p>
              </div>
              <div className="p-2 bg-green-50 rounded-lg">
                <p className="text-sm font-medium text-green-800">Q2 2025</p>
                <p className="text-xs text-green-600">Healthy Options Menu</p>
              </div>
              <div className="p-2 bg-purple-50 rounded-lg">
                <p className="text-sm font-medium text-purple-800">Q3 2025</p>
                <p className="text-xs text-purple-600">Seasonal Specials</p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* KPI Summary */}
      <div className="mt-8">
        <KPISummary period={selectedPeriod} userType={userType} />
      </div>
    </div>
  );
};

export default KPICharts;

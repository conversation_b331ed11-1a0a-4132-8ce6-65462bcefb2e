import React, { useState } from 'react';
import { <PERSON>, Card<PERSON>ontent, Card<PERSON><PERSON>er, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { ChartContainer, ChartTooltip, ChartTooltipContent } from '@/components/ui/chart';
import {
  LineChart,
  Line,
  AreaChart,
  Area,
  BarChart,
  Bar,
  PieChart,
  Pie,
  Cell,
  XAxis,
  YAxis,
  CartesianGrid,
  ResponsiveContainer,
  Legend
} from 'recharts';
import { TrendingUp, TrendingDown, Calendar, DollarSign, Users, Package, Target } from 'lucide-react';
import KPISummary from './KPISummary';

// Mock data for different time periods
const generateSalesData = (period: 'MTD' | 'QTD' | 'YTD') => {
  const baseData = {
    MTD: [
      { name: 'Week 1', sales: 180000, target: 200000, franchisees: 45 },
      { name: 'Week 2', sales: 220000, target: 200000, franchisees: 48 },
      { name: 'Week 3', sales: 195000, target: 200000, franchisees: 46 },
      { name: 'Week 4', sales: 240000, target: 200000, franchisees: 52 }
    ],
    QTD: [
      { name: 'Jan', sales: 850000, target: 800000, franchisees: 45 },
      { name: 'Feb', sales: 920000, target: 850000, franchisees: 48 },
      { name: 'Mar', sales: 1050000, target: 900000, franchisees: 52 }
    ],
    YTD: [
      { name: 'Q1', sales: 2820000, target: 2550000, franchisees: 45 },
      { name: 'Q2', sales: 3150000, target: 2800000, franchisees: 58 },
      { name: 'Q3', sales: 3420000, target: 3000000, franchisees: 62 },
      { name: 'Q4', sales: 2890000, target: 3200000, franchisees: 65 }
    ]
  };
  return baseData[period];
};

const brandPerformanceData = [
  { name: 'Siomai Shop', value: 35, sales: 980000, color: '#ef4444' },
  { name: 'Coffee Shop', value: 28, sales: 750000, color: '#f59e0b' },
  { name: 'Burger & Fries', value: 22, sales: 560000, color: '#10b981' },
  { name: 'Lemon Juice Stand', value: 15, sales: 380000, color: '#eab308' }
];

const kpiData = {
  MTD: {
    totalSales: 835000,
    growth: 12.5,
    target: 800000,
    franchisees: 52,
    avgPerFranchisee: 16058
  },
  QTD: {
    totalSales: 2820000,
    growth: 18.2,
    target: 2550000,
    franchisees: 52,
    avgPerFranchisee: 54231
  },
  YTD: {
    totalSales: 12280000,
    growth: 15.8,
    target: 11550000,
    franchisees: 65,
    avgPerFranchisee: 188923
  }
};

interface KPIChartsProps {
  userType?: 'franchisor' | 'franchisee';
}

const KPICharts: React.FC<KPIChartsProps> = ({ userType = 'franchisor' }) => {
  const [selectedPeriod, setSelectedPeriod] = useState<'MTD' | 'QTD' | 'YTD'>('MTD');

  const currentData = generateSalesData(selectedPeriod);
  const currentKPI = kpiData[selectedPeriod];
  const targetAchievement = (currentKPI.totalSales / currentKPI.target) * 100;

  const formatCurrency = (value: number) => {
    return new Intl.NumberFormat('en-PH', {
      style: 'currency',
      currency: 'PHP',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0
    }).format(value);
  };

  const chartConfig = {
    sales: {
      label: "Sales",
      color: "#2563eb",
    },
    target: {
      label: "Target",
      color: "#dc2626",
    },
    franchisees: {
      label: "Active Franchisees",
      color: "#16a34a",
    }
  };

  return (
    <div className="space-y-6">
      {/* Period Selection */}
      <div className="flex items-center justify-between">
        <h2 className="text-2xl font-bold text-gray-900">Analytics Dashboard</h2>
        <Tabs value={selectedPeriod} onValueChange={(value) => setSelectedPeriod(value as 'MTD' | 'QTD' | 'YTD')}>
          <TabsList className="grid w-full grid-cols-3">
            <TabsTrigger value="MTD">Month to Date</TabsTrigger>
            <TabsTrigger value="QTD">Quarter to Date</TabsTrigger>
            <TabsTrigger value="YTD">Year to Date</TabsTrigger>
          </TabsList>
        </Tabs>
      </div>

      {/* KPI Summary Cards */}
      <div className="grid md:grid-cols-4 gap-6">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Sales ({selectedPeriod})</CardTitle>
            <DollarSign className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{formatCurrency(currentKPI.totalSales)}</div>
            <div className="flex items-center space-x-2 text-xs">
              {currentKPI.growth > 0 ? (
                <TrendingUp className="h-3 w-3 text-green-600" />
              ) : (
                <TrendingDown className="h-3 w-3 text-red-600" />
              )}
              <span className={currentKPI.growth > 0 ? 'text-green-600' : 'text-red-600'}>
                {currentKPI.growth > 0 ? '+' : ''}{currentKPI.growth}% vs last period
              </span>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Target Achievement</CardTitle>
            <Target className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{targetAchievement.toFixed(1)}%</div>
            <div className="text-xs text-muted-foreground">
              Target: {formatCurrency(currentKPI.target)}
            </div>
            <div className="w-full bg-gray-200 rounded-full h-2 mt-2">
              <div
                className={`h-2 rounded-full ${targetAchievement >= 100 ? 'bg-green-500' : targetAchievement >= 80 ? 'bg-yellow-500' : 'bg-red-500'}`}
                style={{ width: `${Math.min(targetAchievement, 100)}%` }}
              ></div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Active Franchisees</CardTitle>
            <Users className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{currentKPI.franchisees}</div>
            <p className="text-xs text-muted-foreground">
              Avg: {formatCurrency(currentKPI.avgPerFranchisee)} per franchisee
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Performance Status</CardTitle>
            <Package className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <Badge className={targetAchievement >= 100 ? 'bg-green-100 text-green-800' : targetAchievement >= 80 ? 'bg-yellow-100 text-yellow-800' : 'bg-red-100 text-red-800'}>
              {targetAchievement >= 100 ? 'Exceeding' : targetAchievement >= 80 ? 'On Track' : 'Below Target'}
            </Badge>
            <p className="text-xs text-muted-foreground mt-2">
              {targetAchievement >= 100 ? '🎉 Great performance!' : targetAchievement >= 80 ? '📈 Good progress' : '⚠️ Needs attention'}
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Charts Grid */}
      <div className="grid lg:grid-cols-2 gap-6">
        {/* Sales Trend Chart */}
        <Card>
          <CardHeader>
            <CardTitle>Sales Trend vs Target ({selectedPeriod})</CardTitle>
          </CardHeader>
          <CardContent>
            <ChartContainer config={chartConfig} className="h-[300px]">
              <LineChart data={currentData}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="name" />
                <YAxis tickFormatter={(value) => `₱${(value / 1000)}K`} />
                <ChartTooltip
                  content={<ChartTooltipContent />}
                  formatter={(value: number, name: string) => [
                    formatCurrency(value),
                    name === 'sales' ? 'Sales' : 'Target'
                  ]}
                />
                <Line
                  type="monotone"
                  dataKey="sales"
                  stroke="var(--color-sales)"
                  strokeWidth={3}
                  dot={{ fill: "var(--color-sales)", strokeWidth: 2, r: 4 }}
                />
                <Line
                  type="monotone"
                  dataKey="target"
                  stroke="var(--color-target)"
                  strokeWidth={2}
                  strokeDasharray="5 5"
                  dot={{ fill: "var(--color-target)", strokeWidth: 2, r: 3 }}
                />
              </LineChart>
            </ChartContainer>
          </CardContent>
        </Card>

        {/* Brand Performance Pie Chart */}
        <Card>
          <CardHeader>
            <CardTitle>Revenue by Brand</CardTitle>
          </CardHeader>
          <CardContent>
            <ChartContainer config={chartConfig} className="h-[300px]">
              <PieChart>
                <Pie
                  data={brandPerformanceData}
                  cx="50%"
                  cy="50%"
                  outerRadius={80}
                  dataKey="value"
                  label={({ name, value }) => `${name}: ${value}%`}
                >
                  {brandPerformanceData.map((entry, index) => (
                    <Cell key={`cell-${index}`} fill={entry.color} />
                  ))}
                </Pie>
                <ChartTooltip
                  content={<ChartTooltipContent />}
                  formatter={(value: number, name: string, props: any) => [
                    `${value}% (${formatCurrency(props.payload.sales)})`,
                    props.payload.name
                  ]}
                />
              </PieChart>
            </ChartContainer>
          </CardContent>
        </Card>

        {/* Franchisee Growth Chart */}
        <Card>
          <CardHeader>
            <CardTitle>Franchisee Growth ({selectedPeriod})</CardTitle>
          </CardHeader>
          <CardContent>
            <ChartContainer config={chartConfig} className="h-[300px]">
              <AreaChart data={currentData}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="name" />
                <YAxis />
                <ChartTooltip
                  content={<ChartTooltipContent />}
                  formatter={(value: number) => [value, 'Active Franchisees']}
                />
                <Area
                  type="monotone"
                  dataKey="franchisees"
                  stroke="var(--color-franchisees)"
                  fill="var(--color-franchisees)"
                  fillOpacity={0.3}
                />
              </AreaChart>
            </ChartContainer>
          </CardContent>
        </Card>

        {/* Performance Comparison Bar Chart */}
        <Card>
          <CardHeader>
            <CardTitle>Sales vs Target Comparison</CardTitle>
          </CardHeader>
          <CardContent>
            <ChartContainer config={chartConfig} className="h-[300px]">
              <BarChart data={currentData}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="name" />
                <YAxis tickFormatter={(value) => `₱${(value / 1000)}K`} />
                <ChartTooltip
                  content={<ChartTooltipContent />}
                  formatter={(value: number, name: string) => [
                    formatCurrency(value),
                    name === 'sales' ? 'Actual Sales' : 'Target'
                  ]}
                />
                <Bar dataKey="target" fill="var(--color-target)" opacity={0.6} />
                <Bar dataKey="sales" fill="var(--color-sales)" />
              </BarChart>
            </ChartContainer>
          </CardContent>
        </Card>
      </div>

      {/* KPI Summary */}
      <div className="mt-8">
        <KPISummary period={selectedPeriod} userType={userType} />
      </div>
    </div>
  );
};

export default KPICharts;

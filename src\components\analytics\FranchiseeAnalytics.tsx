import React, { useState } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { ChartContainer, ChartTooltip, ChartTooltipContent } from '@/components/ui/chart';
import {
  LineChart,
  Line,
  AreaChart,
  Area,
  BarChart,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  ResponsiveContainer
} from 'recharts';
import { TrendingUp, TrendingDown, Calendar, DollarSign, ShoppingCart, Target, Award } from 'lucide-react';
import KPISummary from './KPISummary';

// Mock data for franchisee-specific analytics
const generateFranchiseeSalesData = (period: 'MTD' | 'QTD' | 'YTD') => {
  const baseData = {
    MTD: [
      { name: 'Week 1', sales: 18500, target: 20000, orders: 145, avgOrder: 128 },
      { name: 'Week 2', sales: 22300, target: 20000, orders: 168, avgOrder: 133 },
      { name: 'Week 3', sales: 19800, target: 20000, orders: 152, avgOrder: 130 },
      { name: 'Week 4', sales: 24100, target: 20000, orders: 178, avgOrder: 135 }
    ],
    QTD: [
      { name: 'Jan', sales: 85200, target: 80000, orders: 643, avgOrder: 132 },
      { name: 'Feb', sales: 92100, target: 85000, orders: 698, avgOrder: 132 },
      { name: 'Mar', sales: 105300, target: 90000, orders: 789, avgOrder: 133 }
    ],
    YTD: [
      { name: 'Q1', sales: 282600, target: 255000, orders: 2130, avgOrder: 133 },
      { name: 'Q2', sales: 315400, target: 280000, orders: 2387, avgOrder: 132 },
      { name: 'Q3', sales: 342800, target: 300000, orders: 2591, avgOrder: 132 },
      { name: 'Q4', sales: 289200, target: 320000, orders: 2201, avgOrder: 131 }
    ]
  };
  return baseData[period];
};

const productPerformanceData = [
  { name: 'Siomai Regular', sales: 45200, orders: 1256, percentage: 35 },
  { name: 'Siomai Spicy', sales: 32800, orders: 892, percentage: 25 },
  { name: 'Rice Meals', sales: 28600, orders: 634, percentage: 22 },
  { name: 'Beverages', sales: 23400, orders: 1124, percentage: 18 }
];

const kpiData = {
  MTD: {
    totalSales: 84700,
    growth: 15.2,
    target: 80000,
    orders: 643,
    avgOrderValue: 132,
    topProduct: 'Siomai Regular'
  },
  QTD: {
    totalSales: 282600,
    growth: 18.8,
    target: 255000,
    orders: 2130,
    avgOrderValue: 133,
    topProduct: 'Siomai Regular'
  },
  YTD: {
    totalSales: 1230000,
    growth: 16.4,
    target: 1155000,
    orders: 9309,
    avgOrderValue: 132,
    topProduct: 'Siomai Regular'
  }
};

interface FranchiseeAnalyticsProps {
  franchiseeName?: string;
}

const FranchiseeAnalytics: React.FC<FranchiseeAnalyticsProps> = ({
  franchiseeName = "Siomai Shop - Makati Branch"
}) => {
  const [selectedPeriod, setSelectedPeriod] = useState<'MTD' | 'QTD' | 'YTD'>('MTD');

  const currentData = generateFranchiseeSalesData(selectedPeriod);
  const currentKPI = kpiData[selectedPeriod];
  const targetAchievement = (currentKPI.totalSales / currentKPI.target) * 100;

  const formatCurrency = (value: number) => {
    return new Intl.NumberFormat('en-PH', {
      style: 'currency',
      currency: 'PHP',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0
    }).format(value);
  };

  const chartConfig = {
    sales: {
      label: "Sales",
      color: "#2563eb",
    },
    target: {
      label: "Target",
      color: "#dc2626",
    },
    orders: {
      label: "Orders",
      color: "#16a34a",
    },
    avgOrder: {
      label: "Avg Order Value",
      color: "#ca8a04",
    }
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold text-gray-900">My Performance Dashboard</h2>
          <p className="text-gray-600">{franchiseeName}</p>
        </div>
        <Tabs value={selectedPeriod} onValueChange={(value) => setSelectedPeriod(value as 'MTD' | 'QTD' | 'YTD')}>
          <TabsList className="grid w-full grid-cols-3">
            <TabsTrigger value="MTD">Month to Date</TabsTrigger>
            <TabsTrigger value="QTD">Quarter to Date</TabsTrigger>
            <TabsTrigger value="YTD">Year to Date</TabsTrigger>
          </TabsList>
        </Tabs>
      </div>

      {/* KPI Summary Cards */}
      <div className="grid md:grid-cols-4 gap-6">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Sales ({selectedPeriod})</CardTitle>
            <DollarSign className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{formatCurrency(currentKPI.totalSales)}</div>
            <div className="flex items-center space-x-2 text-xs">
              {currentKPI.growth > 0 ? (
                <TrendingUp className="h-3 w-3 text-green-600" />
              ) : (
                <TrendingDown className="h-3 w-3 text-red-600" />
              )}
              <span className={currentKPI.growth > 0 ? 'text-green-600' : 'text-red-600'}>
                {currentKPI.growth > 0 ? '+' : ''}{currentKPI.growth}% vs last period
              </span>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Target Achievement</CardTitle>
            <Target className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{targetAchievement.toFixed(1)}%</div>
            <div className="text-xs text-muted-foreground">
              Target: {formatCurrency(currentKPI.target)}
            </div>
            <div className="w-full bg-gray-200 rounded-full h-2 mt-2">
              <div
                className={`h-2 rounded-full ${targetAchievement >= 100 ? 'bg-green-500' : targetAchievement >= 80 ? 'bg-yellow-500' : 'bg-red-500'}`}
                style={{ width: `${Math.min(targetAchievement, 100)}%` }}
              ></div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Orders</CardTitle>
            <ShoppingCart className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{currentKPI.orders.toLocaleString()}</div>
            <p className="text-xs text-muted-foreground">
              Avg: {formatCurrency(currentKPI.avgOrderValue)} per order
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Performance Status</CardTitle>
            <Award className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <Badge className={targetAchievement >= 100 ? 'bg-green-100 text-green-800' : targetAchievement >= 80 ? 'bg-yellow-100 text-yellow-800' : 'bg-red-100 text-red-800'}>
              {targetAchievement >= 100 ? 'Exceeding' : targetAchievement >= 80 ? 'On Track' : 'Below Target'}
            </Badge>
            <p className="text-xs text-muted-foreground mt-2">
              Top Product: {currentKPI.topProduct}
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Charts Grid */}
      <div className="grid lg:grid-cols-2 gap-6">
        {/* Sales Trend Chart */}
        <Card>
          <CardHeader>
            <CardTitle>Sales Performance ({selectedPeriod})</CardTitle>
          </CardHeader>
          <CardContent>
            <ChartContainer config={chartConfig} className="h-[300px]">
              <LineChart data={currentData}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="name" />
                <YAxis tickFormatter={(value) => `₱${(value / 1000)}K`} />
                <ChartTooltip
                  content={<ChartTooltipContent />}
                  formatter={(value: number, name: string) => [
                    formatCurrency(value),
                    name === 'sales' ? 'Sales' : 'Target'
                  ]}
                />
                <Line
                  type="monotone"
                  dataKey="sales"
                  stroke="var(--color-sales)"
                  strokeWidth={3}
                  dot={{ fill: "var(--color-sales)", strokeWidth: 2, r: 4 }}
                />
                <Line
                  type="monotone"
                  dataKey="target"
                  stroke="var(--color-target)"
                  strokeWidth={2}
                  strokeDasharray="5 5"
                  dot={{ fill: "var(--color-target)", strokeWidth: 2, r: 3 }}
                />
              </LineChart>
            </ChartContainer>
          </CardContent>
        </Card>

        {/* Orders Trend Chart */}
        <Card>
          <CardHeader>
            <CardTitle>Order Volume ({selectedPeriod})</CardTitle>
          </CardHeader>
          <CardContent>
            <ChartContainer config={chartConfig} className="h-[300px]">
              <AreaChart data={currentData}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="name" />
                <YAxis />
                <ChartTooltip
                  content={<ChartTooltipContent />}
                  formatter={(value: number) => [value, 'Orders']}
                />
                <Area
                  type="monotone"
                  dataKey="orders"
                  stroke="var(--color-orders)"
                  fill="var(--color-orders)"
                  fillOpacity={0.3}
                />
              </AreaChart>
            </ChartContainer>
          </CardContent>
        </Card>

        {/* Product Performance */}
        <Card>
          <CardHeader>
            <CardTitle>Top Products ({selectedPeriod})</CardTitle>
          </CardHeader>
          <CardContent>
            <ChartContainer config={chartConfig} className="h-[300px]">
              <BarChart data={productPerformanceData} layout="horizontal">
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis type="number" tickFormatter={(value) => `₱${(value / 1000)}K`} />
                <YAxis dataKey="name" type="category" width={100} />
                <ChartTooltip
                  content={<ChartTooltipContent />}
                  formatter={(value: number) => [formatCurrency(value), 'Sales']}
                />
                <Bar dataKey="sales" fill="var(--color-sales)" />
              </BarChart>
            </ChartContainer>
          </CardContent>
        </Card>

        {/* Average Order Value Trend */}
        <Card>
          <CardHeader>
            <CardTitle>Average Order Value ({selectedPeriod})</CardTitle>
          </CardHeader>
          <CardContent>
            <ChartContainer config={chartConfig} className="h-[300px]">
              <LineChart data={currentData}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="name" />
                <YAxis tickFormatter={(value) => `₱${value}`} />
                <ChartTooltip
                  content={<ChartTooltipContent />}
                  formatter={(value: number) => [formatCurrency(value), 'Avg Order Value']}
                />
                <Line
                  type="monotone"
                  dataKey="avgOrder"
                  stroke="var(--color-avgOrder)"
                  strokeWidth={3}
                  dot={{ fill: "var(--color-avgOrder)", strokeWidth: 2, r: 4 }}
                />
              </LineChart>
            </ChartContainer>
          </CardContent>
        </Card>
      </div>

      {/* KPI Summary */}
      <div className="mt-8">
        <KPISummary period={selectedPeriod} userType="franchisee" />
      </div>
    </div>
  );
};

export default FranchiseeAnalytics;

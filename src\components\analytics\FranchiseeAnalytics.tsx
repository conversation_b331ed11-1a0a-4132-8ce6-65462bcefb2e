import React, { useState } from 'react';
import { <PERSON>, Card<PERSON>ontent, Card<PERSON><PERSON>er, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { ChartContainer, ChartTooltip, ChartTooltipContent } from '@/components/ui/chart';
import {
  LineChart,
  Line,
  AreaChart,
  Area,
  BarChart,
  Bar,
  PieChart,
  Pie,
  Cell,
  XAxis,
  YAxis,
  CartesianGrid,
  ResponsiveContainer
} from 'recharts';
import { TrendingUp, TrendingDown, Calendar, DollarSign, ShoppingCart, Target, Award } from 'lucide-react';
import KPISummary from './KPISummary';

// Enhanced franchisee analytics with impressive demo data
const generateFranchiseeSalesData = (period: 'MTD' | 'QTD' | 'YTD') => {
  const baseData = {
    MTD: [
      { name: 'Week 1', sales: 285000, target: 250000, orders: 1245, avgOrder: 229, growth: 18.5 },
      { name: 'Week 2', sales: 342000, target: 250000, orders: 1468, avgOrder: 233, growth: 22.8 },
      { name: 'Week 3', sales: 318000, target: 250000, orders: 1352, avgOrder: 235, growth: 19.2 },
      { name: 'Week 4', sales: 425000, target: 250000, orders: 1789, avgOrder: 238, growth: 28.4 }
    ],
    QTD: [
      { name: 'January', sales: 1285000, target: 1150000, orders: 5643, avgOrder: 228, growth: 15.2 },
      { name: 'February', sales: 1492000, target: 1200000, orders: 6298, avgOrder: 237, growth: 18.7 },
      { name: 'March', sales: 1675000, target: 1350000, orders: 6989, avgOrder: 240, growth: 22.4 }
    ],
    YTD: [
      { name: 'Q1 2024', sales: 4452000, target: 3700000, orders: 18930, avgOrder: 235, growth: 20.3 },
      { name: 'Q2 2024', sales: 5215000, target: 4200000, orders: 21987, avgOrder: 237, growth: 24.2 },
      { name: 'Q3 2024', sales: 5842000, target: 4800000, orders: 24591, avgOrder: 238, growth: 21.8 },
      { name: 'Q4 2024', sales: 4890000, target: 5200000, orders: 20601, avgOrder: 237, growth: 16.5 }
    ]
  };
  return baseData[period];
};

// Enhanced product performance data with comprehensive metrics
const generateProductData = (period: 'MTD' | 'QTD' | 'YTD') => {
  const baseData = {
    MTD: [
      { name: 'Siomai King Special', sales: 520000, orders: 2156, percentage: 38, growth: 24.5, profit: 187200, rating: 4.9, category: 'Signature' },
      { name: 'Spicy Siomai Deluxe', sales: 370000, orders: 1642, percentage: 27, growth: 31.2, profit: 133200, rating: 4.8, category: 'Signature' },
      { name: 'Premium Rice Meals', sales: 274000, orders: 1012, percentage: 20, growth: 18.7, profit: 93160, rating: 4.7, category: 'Meals' },
      { name: 'Fresh Beverages', sales: 206000, orders: 1044, percentage: 15, growth: 22.1, profit: 82400, rating: 4.6, category: 'Beverages' }
    ],
    QTD: [
      { name: 'Siomai King Special', sales: 1692000, orders: 7256, percentage: 38, growth: 24.5, profit: 609120, rating: 4.9, category: 'Signature' },
      { name: 'Spicy Siomai Deluxe', sales: 1202000, orders: 5892, percentage: 27, growth: 31.2, profit: 432720, rating: 4.8, category: 'Signature' },
      { name: 'Premium Rice Meals', sales: 890000, orders: 3634, percentage: 20, growth: 18.7, profit: 320400, rating: 4.7, category: 'Meals' },
      { name: 'Fresh Beverages', sales: 668000, orders: 4124, percentage: 15, growth: 22.1, profit: 267200, rating: 4.6, category: 'Beverages' }
    ],
    YTD: [
      { name: 'Siomai King Special', sales: 7751000, orders: 32856, percentage: 38, growth: 24.5, profit: 2790360, rating: 4.9, category: 'Signature' },
      { name: 'Spicy Siomai Deluxe', sales: 5508000, orders: 26742, percentage: 27, growth: 31.2, profit: 1982880, rating: 4.8, category: 'Signature' },
      { name: 'Premium Rice Meals', sales: 4080000, orders: 16489, percentage: 20, growth: 18.7, profit: 1468800, rating: 4.7, category: 'Meals' },
      { name: 'Fresh Beverages', sales: 3060000, orders: 18722, percentage: 15, growth: 22.1, profit: 1224000, rating: 4.6, category: 'Beverages' }
    ]
  };
  return baseData[period];
};

// Additional product categories for comprehensive visualization
const productCategoryData = [
  { category: 'Signature Items', sales: 13259000, percentage: 65, items: 8, avgPrice: 185 },
  { category: 'Rice Meals', sales: 4080000, percentage: 20, items: 12, avgPrice: 247 },
  { category: 'Beverages', sales: 3060000, percentage: 15, items: 15, avgPrice: 163 }
];

// Top selling items across all categories
const topSellingItems = [
  { name: 'Siomai King Special (6pcs)', sales: 2890000, orders: 12456, price: 232, category: 'Signature' },
  { name: 'Spicy Siomai Deluxe (6pcs)', sales: 2618000, orders: 11287, price: 232, category: 'Signature' },
  { name: 'Premium Beef Rice Bowl', sales: 1845000, orders: 7234, price: 255, category: 'Meals' },
  { name: 'Chicken Teriyaki Rice', sales: 1634000, orders: 6892, price: 237, category: 'Meals' },
  { name: 'Fresh Lemonade (Large)', sales: 1289000, orders: 8456, price: 152, category: 'Beverages' },
  { name: 'Iced Coffee Premium', sales: 1156000, orders: 7234, price: 160, category: 'Beverages' },
  { name: 'Siomai King Combo', sales: 1089000, orders: 4567, price: 238, category: 'Signature' },
  { name: 'Pork Sisig Rice', sales: 967000, orders: 4123, price: 234, category: 'Meals' }
];

// Product performance trends over time
const productTrendData = [
  { month: 'Jan', siomaiSpecial: 2450000, spicyDeluxe: 1890000, riceMeals: 1234000, beverages: 987000 },
  { month: 'Feb', siomaiSpecial: 2680000, spicyDeluxe: 2100000, riceMeals: 1345000, beverages: 1089000 },
  { month: 'Mar', siomaiSpecial: 2890000, spicyDeluxe: 2280000, riceMeals: 1456000, beverages: 1178000 },
  { month: 'Apr', siomaiSpecial: 3120000, spicyDeluxe: 2450000, riceMeals: 1567000, beverages: 1267000 },
  { month: 'May', siomaiSpecial: 3350000, spicyDeluxe: 2620000, riceMeals: 1678000, beverages: 1356000 },
  { month: 'Jun', siomaiSpecial: 3180000, spicyDeluxe: 2580000, riceMeals: 1634000, beverages: 1289000 }
];

const kpiData = {
  MTD: {
    totalSales: 1370000,
    growth: 22.1,
    target: 1000000,
    orders: 5854,
    avgOrderValue: 234,
    topProduct: 'Siomai King Special',
    ranking: 'Top 5%',
    customerSatisfaction: 4.8
  },
  QTD: {
    totalSales: 4452000,
    growth: 18.9,
    target: 3700000,
    orders: 18930,
    avgOrderValue: 235,
    topProduct: 'Siomai King Special',
    ranking: 'Top 3%',
    customerSatisfaction: 4.9
  },
  YTD: {
    totalSales: 20399000,
    growth: 20.7,
    target: 17900000,
    orders: 86109,
    avgOrderValue: 237,
    topProduct: 'Siomai King Special',
    ranking: 'Top 1%',
    customerSatisfaction: 4.9
  }
};

interface FranchiseeAnalyticsProps {
  franchiseeName?: string;
}

const FranchiseeAnalytics: React.FC<FranchiseeAnalyticsProps> = ({
  franchiseeName = "Siomai King - Makati Branch"
}) => {
  const [selectedPeriod, setSelectedPeriod] = useState<'MTD' | 'QTD' | 'YTD'>('MTD');

  const currentData = generateFranchiseeSalesData(selectedPeriod);
  const currentProductData = generateProductData(selectedPeriod);
  const currentKPI = kpiData[selectedPeriod];
  const targetAchievement = (currentKPI.totalSales / currentKPI.target) * 100;

  // Enhanced product data that scales with period
  const getProductDataForPeriod = (period: 'MTD' | 'QTD' | 'YTD') => {
    const baseData = currentProductData && currentProductData.length > 0 ? currentProductData : [
      { name: 'Siomai King Special', sales: 520000, orders: 2156, percentage: 38, growth: 24.5, profit: 187200, rating: 4.9, category: 'Signature' },
      { name: 'Spicy Siomai Deluxe', sales: 370000, orders: 1642, percentage: 27, growth: 31.2, profit: 133200, rating: 4.8, category: 'Signature' },
      { name: 'Premium Rice Meals', sales: 274000, orders: 1012, percentage: 20, growth: 18.7, profit: 93160, rating: 4.7, category: 'Meals' },
      { name: 'Fresh Beverages', sales: 206000, orders: 1044, percentage: 15, growth: 22.1, profit: 82400, rating: 4.6, category: 'Beverages' }
    ];

    // Scale data based on period
    const multiplier = period === 'YTD' ? 12 : period === 'QTD' ? 3 : 1;
    return baseData.map(item => ({
      ...item,
      sales: Math.round(item.sales * multiplier),
      orders: Math.round(item.orders * multiplier),
      profit: Math.round(item.profit * multiplier)
    }));
  };

  const productDataToUse = getProductDataForPeriod(selectedPeriod);

  const formatCurrency = (value: number) => {
    return new Intl.NumberFormat('en-PH', {
      style: 'currency',
      currency: 'PHP',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0
    }).format(value);
  };

  const chartConfig = {
    sales: {
      label: "Sales",
      color: "#2563eb",
    },
    target: {
      label: "Target",
      color: "#dc2626",
    },
    orders: {
      label: "Orders",
      color: "#16a34a",
    },
    avgOrder: {
      label: "Avg Order Value",
      color: "#ca8a04",
    }
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold text-gray-900">My Performance Dashboard</h2>
          <p className="text-gray-600">{franchiseeName}</p>
        </div>
        <Tabs value={selectedPeriod} onValueChange={(value) => setSelectedPeriod(value as 'MTD' | 'QTD' | 'YTD')}>
          <TabsList className="grid w-full grid-cols-3">
            <TabsTrigger value="MTD">Month to Date</TabsTrigger>
            <TabsTrigger value="QTD">Quarter to Date</TabsTrigger>
            <TabsTrigger value="YTD">Year to Date</TabsTrigger>
          </TabsList>
        </Tabs>
      </div>

      {/* KPI Summary Cards */}
      <div className="grid md:grid-cols-4 gap-6">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Sales ({selectedPeriod})</CardTitle>
            <DollarSign className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{formatCurrency(currentKPI.totalSales)}</div>
            <div className="flex items-center space-x-2 text-xs">
              {currentKPI.growth > 0 ? (
                <TrendingUp className="h-3 w-3 text-green-600" />
              ) : (
                <TrendingDown className="h-3 w-3 text-red-600" />
              )}
              <span className={currentKPI.growth > 0 ? 'text-green-600' : 'text-red-600'}>
                {currentKPI.growth > 0 ? '+' : ''}{currentKPI.growth}% vs last period
              </span>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Target Achievement</CardTitle>
            <Target className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{targetAchievement.toFixed(1)}%</div>
            <div className="text-xs text-muted-foreground">
              Target: {formatCurrency(currentKPI.target)}
            </div>
            <div className="w-full bg-gray-200 rounded-full h-2 mt-2">
              <div
                className={`h-2 rounded-full ${targetAchievement >= 100 ? 'bg-green-500' : targetAchievement >= 80 ? 'bg-yellow-500' : 'bg-red-500'}`}
                style={{ width: `${Math.min(targetAchievement, 100)}%` }}
              ></div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Orders</CardTitle>
            <ShoppingCart className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{currentKPI.orders.toLocaleString()}</div>
            <p className="text-xs text-muted-foreground">
              Avg: {formatCurrency(currentKPI.avgOrderValue)} per order
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Performance Status</CardTitle>
            <Award className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <Badge className={targetAchievement >= 100 ? 'bg-green-100 text-green-800' : targetAchievement >= 80 ? 'bg-yellow-100 text-yellow-800' : 'bg-red-100 text-red-800'}>
              {targetAchievement >= 100 ? 'Exceeding' : targetAchievement >= 80 ? 'On Track' : 'Below Target'}
            </Badge>
            <p className="text-xs text-muted-foreground mt-2">
              Top Product: {currentKPI.topProduct}
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Essential Performance Charts - World Class Design */}
      <div className="grid lg:grid-cols-2 gap-8">
        {/* Revenue vs Target - Primary Chart */}
        <Card className="border-0 shadow-xl bg-gradient-to-br from-blue-50 to-indigo-50">
          <CardHeader className="pb-4">
            <CardTitle className="text-xl font-bold text-gray-800 flex items-center">
              <TrendingUp className="w-6 h-6 mr-3 text-blue-600" />
              Revenue Performance ({selectedPeriod})
            </CardTitle>
          </CardHeader>
          <CardContent>
            <ChartContainer config={chartConfig} className="h-[400px]">
              <ResponsiveContainer width="100%" height="100%">
                <LineChart data={currentData} margin={{ top: 20, right: 30, left: 20, bottom: 5 }}>
                  <defs>
                    <linearGradient id="revenueGradient" x1="0" y1="0" x2="0" y2="1">
                      <stop offset="5%" stopColor="#3b82f6" stopOpacity={0.8}/>
                      <stop offset="95%" stopColor="#3b82f6" stopOpacity={0.1}/>
                    </linearGradient>
                  </defs>
                  <CartesianGrid strokeDasharray="3 3" stroke="#e2e8f0" />
                  <XAxis
                    dataKey="name"
                    axisLine={false}
                    tickLine={false}
                    tick={{ fontSize: 12, fill: '#64748b' }}
                  />
                  <YAxis
                    tickFormatter={(value) => `₱${(value / 1000)}K`}
                    axisLine={false}
                    tickLine={false}
                    tick={{ fontSize: 12, fill: '#64748b' }}
                  />
                  <ChartTooltip
                    content={<ChartTooltipContent />}
                    formatter={(value: number, name: string) => [
                      formatCurrency(value),
                      name === 'sales' ? 'Revenue' : 'Target'
                    ]}
                  />
                  <Area
                    type="monotone"
                    dataKey="sales"
                    stroke="#3b82f6"
                    strokeWidth={3}
                    fill="url(#revenueGradient)"
                    fillOpacity={0.3}
                  />
                  <Line
                    type="monotone"
                    dataKey="target"
                    stroke="#ef4444"
                    strokeWidth={2}
                    strokeDasharray="8 4"
                    dot={{ fill: "#ef4444", strokeWidth: 2, r: 4 }}
                  />
                </LineChart>
              </ResponsiveContainer>
            </ChartContainer>
          </CardContent>
        </Card>

        {/* Top Products - Essential Chart */}
        <Card className="border-0 shadow-xl bg-gradient-to-br from-green-50 to-emerald-50">
          <CardHeader className="pb-4">
            <CardTitle className="text-xl font-bold text-gray-800 flex items-center">
              <ShoppingCart className="w-6 h-6 mr-3 text-green-600" />
              Top Products ({selectedPeriod})
            </CardTitle>
          </CardHeader>
          <CardContent>
            <ChartContainer config={chartConfig} className="h-[400px]">
              <ResponsiveContainer width="100%" height="100%">
                <BarChart
                  data={productDataToUse.slice(0, 5)}
                  layout="horizontal"
                  margin={{ top: 20, right: 30, left: 20, bottom: 5 }}
                >
                  <CartesianGrid strokeDasharray="3 3" stroke="#e2e8f0" />
                  <XAxis
                    type="number"
                    tickFormatter={(value) => `₱${(value / 1000)}K`}
                    axisLine={false}
                    tickLine={false}
                    tick={{ fontSize: 12, fill: '#64748b' }}
                  />
                  <YAxis
                    dataKey="name"
                    type="category"
                    width={140}
                    axisLine={false}
                    tickLine={false}
                    tick={{ fontSize: 12, fill: '#64748b' }}
                  />
                  <ChartTooltip
                    content={<ChartTooltipContent />}
                    formatter={(value: number, name: string) => [
                      formatCurrency(value),
                      'Revenue'
                    ]}
                  />
                  <Bar
                    dataKey="sales"
                    fill="#10b981"
                    radius={[0, 8, 8, 0]}
                  />
                </BarChart>
              </ResponsiveContainer>
            </ChartContainer>
          </CardContent>
        </Card>
      </div>

      {/* Business Insights - Streamlined */}
      <div className="grid lg:grid-cols-3 gap-8 mt-8">
        {/* Performance Status */}
        <Card className="border-0 shadow-xl bg-gradient-to-br from-purple-50 to-pink-50">
          <CardHeader className="pb-4">
            <CardTitle className="text-xl font-bold text-gray-800 flex items-center">
              <Target className="w-6 h-6 mr-3 text-purple-600" />
              Performance Status
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-center space-y-4">
              <div className="p-6 bg-white rounded-xl shadow-sm">
                <div className="text-3xl font-bold text-purple-600">{targetAchievement.toFixed(1)}%</div>
                <p className="text-gray-600 mt-1">Target Achievement</p>
                <Badge className={`mt-3 px-4 py-2 ${targetAchievement >= 100 ? 'bg-green-100 text-green-800' : targetAchievement >= 80 ? 'bg-yellow-100 text-yellow-800' : 'bg-red-100 text-red-800'}`}>
                  {targetAchievement >= 100 ? 'Exceeding Target' : targetAchievement >= 80 ? 'On Track' : 'Below Target'}
                </Badge>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Top Product */}
        <Card className="border-0 shadow-xl bg-gradient-to-br from-yellow-50 to-orange-50">
          <CardHeader className="pb-4">
            <CardTitle className="text-xl font-bold text-gray-800 flex items-center">
              <Award className="w-6 h-6 mr-3 text-yellow-600" />
              Star Product
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-center space-y-4">
              <div className="p-6 bg-white rounded-xl shadow-sm">
                <div className="text-xl font-bold text-gray-800">Siomai King Special</div>
                <div className="text-3xl font-bold text-yellow-600 mt-2">{formatCurrency(productDataToUse[0]?.sales || 0)}</div>
                <p className="text-gray-600 mt-1">Revenue ({selectedPeriod})</p>
                <div className="flex justify-center space-x-2 mt-4">
                  <Badge className="bg-green-100 text-green-800">38% of sales</Badge>
                  <Badge className="bg-blue-100 text-blue-800">4.9★ rating</Badge>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Growth Metrics */}
        <Card className="border-0 shadow-xl bg-gradient-to-br from-emerald-50 to-teal-50">
          <CardHeader className="pb-4">
            <CardTitle className="text-xl font-bold text-gray-800 flex items-center">
              <TrendingUp className="w-6 h-6 mr-3 text-emerald-600" />
              Growth Highlights
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="p-4 bg-white rounded-xl shadow-sm">
                <div className="flex items-center justify-between">
                  <span className="text-gray-600">Revenue Growth</span>
                  <span className="text-2xl font-bold text-emerald-600">+{currentKPI.growth}%</span>
                </div>
              </div>
              <div className="p-4 bg-white rounded-xl shadow-sm">
                <div className="flex items-center justify-between">
                  <span className="text-gray-600">Avg Order Value</span>
                  <span className="text-2xl font-bold text-blue-600">{formatCurrency(currentKPI.avgOrderValue)}</span>
                </div>
              </div>
              <div className="p-4 bg-white rounded-xl shadow-sm">
                <div className="flex items-center justify-between">
                  <span className="text-gray-600">Customer Rating</span>
                  <span className="text-2xl font-bold text-purple-600">{currentKPI.customerSatisfaction}★</span>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* KPI Summary */}
      <div className="mt-8">
        <KPISummary period={selectedPeriod} userType="franchisee" />
      </div>
    </div>
  );
};

export default FranchiseeAnalytics;

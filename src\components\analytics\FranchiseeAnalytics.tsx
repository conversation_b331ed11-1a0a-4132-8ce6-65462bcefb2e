import React, { useState } from 'react';
import { <PERSON>, Card<PERSON>ontent, Card<PERSON><PERSON>er, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { ChartContainer, ChartTooltip, ChartTooltipContent } from '@/components/ui/chart';
import {
  LineChart,
  Line,
  AreaChart,
  Area,
  BarChart,
  Bar,
  PieChart,
  Pie,
  Cell,
  XAxis,
  YAxis,
  CartesianGrid,
  ResponsiveContainer
} from 'recharts';
import { TrendingUp, TrendingDown, Calendar, DollarSign, ShoppingCart, Target, Award } from 'lucide-react';
import KPISummary from './KPISummary';

// Enhanced franchisee analytics with impressive demo data
const generateFranchiseeSalesData = (period: 'MTD' | 'QTD' | 'YTD') => {
  const baseData = {
    MTD: [
      { name: 'Week 1', sales: 285000, target: 250000, orders: 1245, avgOrder: 229, growth: 18.5 },
      { name: 'Week 2', sales: 342000, target: 250000, orders: 1468, avgOrder: 233, growth: 22.8 },
      { name: 'Week 3', sales: 318000, target: 250000, orders: 1352, avgOrder: 235, growth: 19.2 },
      { name: 'Week 4', sales: 425000, target: 250000, orders: 1789, avgOrder: 238, growth: 28.4 }
    ],
    QTD: [
      { name: 'January', sales: 1285000, target: 1150000, orders: 5643, avgOrder: 228, growth: 15.2 },
      { name: 'February', sales: 1492000, target: 1200000, orders: 6298, avgOrder: 237, growth: 18.7 },
      { name: 'March', sales: 1675000, target: 1350000, orders: 6989, avgOrder: 240, growth: 22.4 }
    ],
    YTD: [
      { name: 'Q1 2024', sales: 4452000, target: 3700000, orders: 18930, avgOrder: 235, growth: 20.3 },
      { name: 'Q2 2024', sales: 5215000, target: 4200000, orders: 21987, avgOrder: 237, growth: 24.2 },
      { name: 'Q3 2024', sales: 5842000, target: 4800000, orders: 24591, avgOrder: 238, growth: 21.8 },
      { name: 'Q4 2024', sales: 4890000, target: 5200000, orders: 20601, avgOrder: 237, growth: 16.5 }
    ]
  };
  return baseData[period];
};

// Enhanced product performance data with comprehensive metrics
const generateProductData = (period: 'MTD' | 'QTD' | 'YTD') => {
  const baseData = {
    MTD: [
      { name: 'Siomai King Special', sales: 520000, orders: 2156, percentage: 38, growth: 24.5, profit: 187200, rating: 4.9, category: 'Signature' },
      { name: 'Spicy Siomai Deluxe', sales: 370000, orders: 1642, percentage: 27, growth: 31.2, profit: 133200, rating: 4.8, category: 'Signature' },
      { name: 'Premium Rice Meals', sales: 274000, orders: 1012, percentage: 20, growth: 18.7, profit: 93160, rating: 4.7, category: 'Meals' },
      { name: 'Fresh Beverages', sales: 206000, orders: 1044, percentage: 15, growth: 22.1, profit: 82400, rating: 4.6, category: 'Beverages' }
    ],
    QTD: [
      { name: 'Siomai King Special', sales: 1692000, orders: 7256, percentage: 38, growth: 24.5, profit: 609120, rating: 4.9, category: 'Signature' },
      { name: 'Spicy Siomai Deluxe', sales: 1202000, orders: 5892, percentage: 27, growth: 31.2, profit: 432720, rating: 4.8, category: 'Signature' },
      { name: 'Premium Rice Meals', sales: 890000, orders: 3634, percentage: 20, growth: 18.7, profit: 320400, rating: 4.7, category: 'Meals' },
      { name: 'Fresh Beverages', sales: 668000, orders: 4124, percentage: 15, growth: 22.1, profit: 267200, rating: 4.6, category: 'Beverages' }
    ],
    YTD: [
      { name: 'Siomai King Special', sales: 7751000, orders: 32856, percentage: 38, growth: 24.5, profit: 2790360, rating: 4.9, category: 'Signature' },
      { name: 'Spicy Siomai Deluxe', sales: 5508000, orders: 26742, percentage: 27, growth: 31.2, profit: 1982880, rating: 4.8, category: 'Signature' },
      { name: 'Premium Rice Meals', sales: 4080000, orders: 16489, percentage: 20, growth: 18.7, profit: 1468800, rating: 4.7, category: 'Meals' },
      { name: 'Fresh Beverages', sales: 3060000, orders: 18722, percentage: 15, growth: 22.1, profit: 1224000, rating: 4.6, category: 'Beverages' }
    ]
  };
  return baseData[period];
};

// Additional product categories for comprehensive visualization
const productCategoryData = [
  { category: 'Signature Items', sales: 13259000, percentage: 65, items: 8, avgPrice: 185 },
  { category: 'Rice Meals', sales: 4080000, percentage: 20, items: 12, avgPrice: 247 },
  { category: 'Beverages', sales: 3060000, percentage: 15, items: 15, avgPrice: 163 }
];

// Top selling items across all categories
const topSellingItems = [
  { name: 'Siomai King Special (6pcs)', sales: 2890000, orders: 12456, price: 232, category: 'Signature' },
  { name: 'Spicy Siomai Deluxe (6pcs)', sales: 2618000, orders: 11287, price: 232, category: 'Signature' },
  { name: 'Premium Beef Rice Bowl', sales: 1845000, orders: 7234, price: 255, category: 'Meals' },
  { name: 'Chicken Teriyaki Rice', sales: 1634000, orders: 6892, price: 237, category: 'Meals' },
  { name: 'Fresh Lemonade (Large)', sales: 1289000, orders: 8456, price: 152, category: 'Beverages' },
  { name: 'Iced Coffee Premium', sales: 1156000, orders: 7234, price: 160, category: 'Beverages' },
  { name: 'Siomai King Combo', sales: 1089000, orders: 4567, price: 238, category: 'Signature' },
  { name: 'Pork Sisig Rice', sales: 967000, orders: 4123, price: 234, category: 'Meals' }
];

// Product performance trends over time
const productTrendData = [
  { month: 'Jan', siomaiSpecial: 2450000, spicyDeluxe: 1890000, riceMeals: 1234000, beverages: 987000 },
  { month: 'Feb', siomaiSpecial: 2680000, spicyDeluxe: 2100000, riceMeals: 1345000, beverages: 1089000 },
  { month: 'Mar', siomaiSpecial: 2890000, spicyDeluxe: 2280000, riceMeals: 1456000, beverages: 1178000 },
  { month: 'Apr', siomaiSpecial: 3120000, spicyDeluxe: 2450000, riceMeals: 1567000, beverages: 1267000 },
  { month: 'May', siomaiSpecial: 3350000, spicyDeluxe: 2620000, riceMeals: 1678000, beverages: 1356000 },
  { month: 'Jun', siomaiSpecial: 3180000, spicyDeluxe: 2580000, riceMeals: 1634000, beverages: 1289000 }
];

const kpiData = {
  MTD: {
    totalSales: 1370000,
    growth: 22.1,
    target: 1000000,
    orders: 5854,
    avgOrderValue: 234,
    topProduct: 'Siomai King Special',
    ranking: 'Top 5%',
    customerSatisfaction: 4.8
  },
  QTD: {
    totalSales: 4452000,
    growth: 18.9,
    target: 3700000,
    orders: 18930,
    avgOrderValue: 235,
    topProduct: 'Siomai King Special',
    ranking: 'Top 3%',
    customerSatisfaction: 4.9
  },
  YTD: {
    totalSales: 20399000,
    growth: 20.7,
    target: 17900000,
    orders: 86109,
    avgOrderValue: 237,
    topProduct: 'Siomai King Special',
    ranking: 'Top 1%',
    customerSatisfaction: 4.9
  }
};

interface FranchiseeAnalyticsProps {
  franchiseeName?: string;
}

const FranchiseeAnalytics: React.FC<FranchiseeAnalyticsProps> = ({
  franchiseeName = "Siomai King - Makati Branch"
}) => {
  const [selectedPeriod, setSelectedPeriod] = useState<'MTD' | 'QTD' | 'YTD'>('MTD');

  const currentData = generateFranchiseeSalesData(selectedPeriod);
  const currentProductData = generateProductData(selectedPeriod);
  const currentKPI = kpiData[selectedPeriod];
  const targetAchievement = (currentKPI.totalSales / currentKPI.target) * 100;

  // Enhanced product data that scales with period
  const getProductDataForPeriod = (period: 'MTD' | 'QTD' | 'YTD') => {
    const baseData = currentProductData && currentProductData.length > 0 ? currentProductData : [
      { name: 'Siomai King Special', sales: 520000, orders: 2156, percentage: 38, growth: 24.5, profit: 187200, rating: 4.9, category: 'Signature' },
      { name: 'Spicy Siomai Deluxe', sales: 370000, orders: 1642, percentage: 27, growth: 31.2, profit: 133200, rating: 4.8, category: 'Signature' },
      { name: 'Premium Rice Meals', sales: 274000, orders: 1012, percentage: 20, growth: 18.7, profit: 93160, rating: 4.7, category: 'Meals' },
      { name: 'Fresh Beverages', sales: 206000, orders: 1044, percentage: 15, growth: 22.1, profit: 82400, rating: 4.6, category: 'Beverages' }
    ];

    // Scale data based on period
    const multiplier = period === 'YTD' ? 12 : period === 'QTD' ? 3 : 1;
    return baseData.map(item => ({
      ...item,
      sales: Math.round(item.sales * multiplier),
      orders: Math.round(item.orders * multiplier),
      profit: Math.round(item.profit * multiplier)
    }));
  };

  const productDataToUse = getProductDataForPeriod(selectedPeriod);

  const formatCurrency = (value: number) => {
    return new Intl.NumberFormat('en-PH', {
      style: 'currency',
      currency: 'PHP',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0
    }).format(value);
  };

  const chartConfig = {
    sales: {
      label: "Sales",
      color: "#2563eb",
    },
    target: {
      label: "Target",
      color: "#dc2626",
    },
    orders: {
      label: "Orders",
      color: "#16a34a",
    },
    avgOrder: {
      label: "Avg Order Value",
      color: "#ca8a04",
    }
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold text-gray-900">My Performance Dashboard</h2>
          <p className="text-gray-600">{franchiseeName}</p>
        </div>
        <Tabs value={selectedPeriod} onValueChange={(value) => setSelectedPeriod(value as 'MTD' | 'QTD' | 'YTD')}>
          <TabsList className="grid w-full grid-cols-3">
            <TabsTrigger value="MTD">Month to Date</TabsTrigger>
            <TabsTrigger value="QTD">Quarter to Date</TabsTrigger>
            <TabsTrigger value="YTD">Year to Date</TabsTrigger>
          </TabsList>
        </Tabs>
      </div>

      {/* KPI Summary Cards */}
      <div className="grid md:grid-cols-4 gap-6">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Sales ({selectedPeriod})</CardTitle>
            <DollarSign className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{formatCurrency(currentKPI.totalSales)}</div>
            <div className="flex items-center space-x-2 text-xs">
              {currentKPI.growth > 0 ? (
                <TrendingUp className="h-3 w-3 text-green-600" />
              ) : (
                <TrendingDown className="h-3 w-3 text-red-600" />
              )}
              <span className={currentKPI.growth > 0 ? 'text-green-600' : 'text-red-600'}>
                {currentKPI.growth > 0 ? '+' : ''}{currentKPI.growth}% vs last period
              </span>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Target Achievement</CardTitle>
            <Target className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{targetAchievement.toFixed(1)}%</div>
            <div className="text-xs text-muted-foreground">
              Target: {formatCurrency(currentKPI.target)}
            </div>
            <div className="w-full bg-gray-200 rounded-full h-2 mt-2">
              <div
                className={`h-2 rounded-full ${targetAchievement >= 100 ? 'bg-green-500' : targetAchievement >= 80 ? 'bg-yellow-500' : 'bg-red-500'}`}
                style={{ width: `${Math.min(targetAchievement, 100)}%` }}
              ></div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Orders</CardTitle>
            <ShoppingCart className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{currentKPI.orders.toLocaleString()}</div>
            <p className="text-xs text-muted-foreground">
              Avg: {formatCurrency(currentKPI.avgOrderValue)} per order
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Performance Status</CardTitle>
            <Award className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <Badge className={targetAchievement >= 100 ? 'bg-green-100 text-green-800' : targetAchievement >= 80 ? 'bg-yellow-100 text-yellow-800' : 'bg-red-100 text-red-800'}>
              {targetAchievement >= 100 ? 'Exceeding' : targetAchievement >= 80 ? 'On Track' : 'Below Target'}
            </Badge>
            <p className="text-xs text-muted-foreground mt-2">
              Top Product: {currentKPI.topProduct}
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Charts Grid */}
      <div className="grid lg:grid-cols-2 gap-6">
        {/* Sales Trend Chart */}
        <Card>
          <CardHeader>
            <CardTitle>Sales Performance ({selectedPeriod})</CardTitle>
          </CardHeader>
          <CardContent>
            <ChartContainer config={chartConfig} className="h-[300px]">
              <LineChart data={currentData}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="name" />
                <YAxis tickFormatter={(value) => `₱${(value / 1000)}K`} />
                <ChartTooltip
                  content={<ChartTooltipContent />}
                  formatter={(value: number, name: string) => [
                    formatCurrency(value),
                    name === 'sales' ? 'Sales' : 'Target'
                  ]}
                />
                <Line
                  type="monotone"
                  dataKey="sales"
                  stroke="var(--color-sales)"
                  strokeWidth={3}
                  dot={{ fill: "var(--color-sales)", strokeWidth: 2, r: 4 }}
                />
                <Line
                  type="monotone"
                  dataKey="target"
                  stroke="var(--color-target)"
                  strokeWidth={2}
                  strokeDasharray="5 5"
                  dot={{ fill: "var(--color-target)", strokeWidth: 2, r: 3 }}
                />
              </LineChart>
            </ChartContainer>
          </CardContent>
        </Card>

        {/* Orders Trend Chart */}
        <Card>
          <CardHeader>
            <CardTitle>Order Volume ({selectedPeriod})</CardTitle>
          </CardHeader>
          <CardContent>
            <ChartContainer config={chartConfig} className="h-[300px]">
              <AreaChart data={currentData}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="name" />
                <YAxis />
                <ChartTooltip
                  content={<ChartTooltipContent />}
                  formatter={(value: number) => [value, 'Orders']}
                />
                <Area
                  type="monotone"
                  dataKey="orders"
                  stroke="var(--color-orders)"
                  fill="var(--color-orders)"
                  fillOpacity={0.3}
                />
              </AreaChart>
            </ChartContainer>
          </CardContent>
        </Card>

        {/* Product Performance */}
        <Card>
          <CardHeader>
            <CardTitle>Top Products ({selectedPeriod})</CardTitle>
          </CardHeader>
          <CardContent>
            <ChartContainer config={chartConfig} className="h-[300px]">
              <BarChart data={productDataToUse} layout="horizontal">
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis type="number" tickFormatter={(value) => `₱${(value / 1000)}K`} />
                <YAxis dataKey="name" type="category" width={120} />
                <ChartTooltip
                  content={<ChartTooltipContent />}
                  formatter={(value: number, name: string, props: any) => {
                    if (name === 'sales') return [formatCurrency(value), 'Sales'];
                    if (name === 'orders') return [value.toLocaleString(), 'Orders'];
                    if (name === 'profit') return [formatCurrency(value), 'Profit'];
                    return [value, name];
                  }}
                />
                <Bar dataKey="sales" fill="var(--color-sales)" />
              </BarChart>
            </ChartContainer>
          </CardContent>
        </Card>

        {/* Average Order Value Trend */}
        <Card>
          <CardHeader>
            <CardTitle>Average Order Value ({selectedPeriod})</CardTitle>
          </CardHeader>
          <CardContent>
            <ChartContainer config={chartConfig} className="h-[300px]">
              <LineChart data={currentData}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="name" />
                <YAxis tickFormatter={(value) => `₱${value}`} />
                <ChartTooltip
                  content={<ChartTooltipContent />}
                  formatter={(value: number) => [formatCurrency(value), 'Avg Order Value']}
                />
                <Line
                  type="monotone"
                  dataKey="avgOrder"
                  stroke="var(--color-avgOrder)"
                  strokeWidth={3}
                  dot={{ fill: "var(--color-avgOrder)", strokeWidth: 2, r: 4 }}
                />
              </LineChart>
            </ChartContainer>
          </CardContent>
        </Card>
      </div>

      {/* Additional Product Analytics */}
      <div className="grid lg:grid-cols-3 gap-6 mt-6">
        {/* Product Categories Pie Chart */}
        <Card>
          <CardHeader>
            <CardTitle>Sales by Category</CardTitle>
          </CardHeader>
          <CardContent>
            <ChartContainer config={chartConfig} className="h-[250px]">
              <PieChart>
                <Pie
                  data={productCategoryData}
                  cx="50%"
                  cy="50%"
                  outerRadius={60}
                  dataKey="percentage"
                  label={({ category, percentage }) => `${category}: ${percentage}%`}
                >
                  {productCategoryData.map((entry, index) => (
                    <Cell key={`cell-${index}`} fill={['#ef4444', '#f59e0b', '#10b981'][index]} />
                  ))}
                </Pie>
                <ChartTooltip
                  content={<ChartTooltipContent />}
                  formatter={(value: number, name: string, props: any) => [
                    `${value}% (${formatCurrency(props.payload.sales)})`,
                    props.payload.category
                  ]}
                />
              </PieChart>
            </ChartContainer>
          </CardContent>
        </Card>

        {/* Top Selling Items */}
        <Card>
          <CardHeader>
            <CardTitle>Best Sellers (YTD)</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {topSellingItems.slice(0, 5).map((item, index) => (
                <div key={index} className="flex items-center justify-between p-2 bg-gray-50 rounded-lg">
                  <div className="flex-1">
                    <p className="font-medium text-sm">{item.name}</p>
                    <p className="text-xs text-gray-600">{item.orders.toLocaleString()} orders</p>
                  </div>
                  <div className="text-right">
                    <p className="font-bold text-sm">{formatCurrency(item.sales)}</p>
                    <p className="text-xs text-gray-600">₱{item.price}</p>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>

        {/* Product Performance Metrics */}
        <Card>
          <CardHeader>
            <CardTitle>Product Insights</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="p-3 bg-green-50 rounded-lg border border-green-200">
                <div className="flex items-center space-x-2">
                  <Award className="w-4 h-4 text-green-600" />
                  <span className="text-sm font-medium text-green-800">Top Performer</span>
                </div>
                <p className="text-xs text-green-700 mt-1">Siomai King Special - 38% of total sales</p>
              </div>

              <div className="p-3 bg-blue-50 rounded-lg border border-blue-200">
                <div className="flex items-center space-x-2">
                  <TrendingUp className="w-4 h-4 text-blue-600" />
                  <span className="text-sm font-medium text-blue-800">Fastest Growing</span>
                </div>
                <p className="text-xs text-blue-700 mt-1">Spicy Siomai Deluxe - 31.2% growth</p>
              </div>

              <div className="p-3 bg-yellow-50 rounded-lg border border-yellow-200">
                <div className="flex items-center space-x-2">
                  <DollarSign className="w-4 h-4 text-yellow-600" />
                  <span className="text-sm font-medium text-yellow-800">Highest Margin</span>
                </div>
                <p className="text-xs text-yellow-700 mt-1">Premium Rice Meals - 36% profit margin</p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Product Trend Chart */}
      <Card className="mt-6">
        <CardHeader>
          <CardTitle>Product Sales Trends (6 Months)</CardTitle>
        </CardHeader>
        <CardContent>
          <ChartContainer config={chartConfig} className="h-[300px]">
            <LineChart data={productTrendData}>
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis dataKey="month" />
              <YAxis tickFormatter={(value) => `₱${(value / 1000)}K`} />
              <ChartTooltip
                content={<ChartTooltipContent />}
                formatter={(value: number, name: string) => [
                  formatCurrency(value),
                  name === 'siomaiSpecial' ? 'Siomai King Special' :
                  name === 'spicyDeluxe' ? 'Spicy Siomai Deluxe' :
                  name === 'riceMeals' ? 'Premium Rice Meals' :
                  name === 'beverages' ? 'Fresh Beverages' : name
                ]}
              />
              <Line type="monotone" dataKey="siomaiSpecial" stroke="#ef4444" strokeWidth={3} />
              <Line type="monotone" dataKey="spicyDeluxe" stroke="#f59e0b" strokeWidth={3} />
              <Line type="monotone" dataKey="riceMeals" stroke="#10b981" strokeWidth={3} />
              <Line type="monotone" dataKey="beverages" stroke="#8b5cf6" strokeWidth={3} />
            </LineChart>
          </ChartContainer>
        </CardContent>
      </Card>

      {/* KPI Summary */}
      <div className="mt-8">
        <KPISummary period={selectedPeriod} userType="franchisee" />
      </div>
    </div>
  );
};

export default FranchiseeAnalytics;
